<div class="login-container">
    <!-- Left Panel - Brand & Info -->
    <div class="left-panel">
        <div class="brand-section">
            <div class="logo-section">
                <div class="logo-placeholder">
                    <span class="logo-text">NISSHIN TECHNOMIC</span>
                    <span class="logo-subtext">VIETNAM</span>
                </div>
            </div>
            <h1 class="brand-title">Debt Management System</h1>
            <h2 class="brand-subtitle">Hệ Thống Quản Lý Công Nợ Chuyên Nghiệp</h2>

            <div class="brand-description">
                <p>Giải pháp quản lý công nợ toàn diện, theo dõi và cảnh báo công nợ</p>
                <p>giúp doanh nghiệp tối ưu hóa dòng tiền và giảm thiểu rủi ro.</p>
            </div>
        </div>

        <!-- Decorative circles -->
        <div class="decorative-circle circle-1"></div>
        <div class="decorative-circle circle-2"></div>
        <div class="decorative-circle circle-3"></div>

        <!-- Bottom modules -->
        <div class="modules-section">
            <div class="module-card">
                <div class="module-title">ORDER MANAGEMENT</div>
                <div class="module-subtitle">Quản lý Đơn đặt hàng</div>
            </div>
            <div class="module-card">
                <div class="module-title">DEBT ALERTS</div>
                <div class="module-subtitle">Cảnh báo công nợ quá hạn</div>
            </div>
        </div>
    </div>

    <!-- Right Panel - Login Form -->
    <div class="right-panel">
        <div class="login-form-container">
            <div class="login-header">
                <h3 class="login-title">Chào mừng trở lại</h3>
                <p class="login-subtitle">Đăng nhập để truy cập hệ thống</p>
            </div>

            <form (ngSubmit)="login()" #loginForm="ngForm" class="login-form">
                <!-- Error message display -->
                <div *ngIf="errorMessage" class="error-message">
                    {{ errorMessage }}
                </div>

                <div class="form-group">
                    <label for="username" class="form-label">Tên đăng nhập</label>
                    <input
                        id="username"
                        name="username"
                        type="text"
                        placeholder="Nhập tên đăng nhập của bạn"
                        [(ngModel)]="username"
                        class="form-input"
                        [disabled]="isLoading"
                        (keypress)="onKeyPress($event)"
                        required />
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="password-wrapper">
                        <input
                            id="password"
                            name="password"
                            [type]="showPassword ? 'text' : 'password'"
                            placeholder="Nhập mật khẩu"
                            [(ngModel)]="password"
                            class="form-input password-input"
                            [disabled]="isLoading"
                            (keypress)="onKeyPress($event)"
                            required />
                        <button type="button" class="password-toggle" (click)="togglePassword()" [disabled]="isLoading">
                            <i [class]="showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" [(ngModel)]="rememberMe" name="rememberMe" />
                        <span class="checkmark"></span>
                        Ghi nhớ đăng nhập
                    </label>
                </div>

                <button
                    type="submit"
                    class="login-button"
                    [disabled]="isLoading || !loginForm.form.valid"
                    [class.loading]="isLoading">
                    <span *ngIf="!isLoading">Đăng nhập</span>
                    <span *ngIf="isLoading">
                        <i class="pi pi-spin pi-spinner"></i>
                        Đang đăng nhập...
                    </span>
                </button>
            </form>
        </div>
    </div>
</div>
